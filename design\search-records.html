<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rechercher Dossiers - Système de Gestion Médicale</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation Header -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-bold text-blue-600">Clinique Santé Plus</h1>
                    </div>
                    <div class="hidden md:ml-6 md:flex md:space-x-8">
                        <a href="index.html" class="text-gray-500 hover:text-gray-700 px-1 pt-1 pb-4 text-sm font-medium">
                            Tableau de bord
                        </a>
                        <a href="patients.html" class="text-gray-500 hover:text-gray-700 px-1 pt-1 pb-4 text-sm font-medium">
                            Patients
                        </a>
                        <a href="add-patient.html" class="text-gray-500 hover:text-gray-700 px-1 pt-1 pb-4 text-sm font-medium">
                            Nouveau patient
                        </a>
                        <a href="search-records.html" class="text-blue-600 border-b-2 border-blue-600 px-1 pt-1 pb-4 text-sm font-medium">
                            Rechercher
                        </a>
                    </div>
                </div>
                <div class="flex items-center">
                    <span class="text-sm text-gray-500">Dr. Aminata Diallo</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Header Section -->
        <div class="px-4 py-6 sm:px-0">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Rechercher Dossiers Médicaux</h2>
                <p class="mt-1 text-sm text-gray-600">Trouvez rapidement les dossiers de vos patients</p>
            </div>
        </div>

        <!-- Search Form -->
        <div class="px-4 sm:px-0 mb-6">
            <div class="bg-white shadow rounded-lg p-6">
                <form class="space-y-6">
                    <!-- Basic Search -->
                    <div>
                        <label for="search_query" class="block text-sm font-medium text-gray-700 mb-2">
                            Recherche générale
                        </label>
                        <input type="text" id="search_query" name="search_query"
                               placeholder="Nom du patient, diagnostic, médicament..."
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <!-- Advanced Filters -->
                    <div class="border-t border-gray-200 pt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Filtres avancés</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <div>
                                <label for="patient_name" class="block text-sm font-medium text-gray-700 mb-1">
                                    Nom du patient
                                </label>
                                <input type="text" id="patient_name" name="patient_name"
                                       placeholder="Ex: Aminata Sow"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            </div>

                            <div>
                                <label for="type_consultation" class="block text-sm font-medium text-gray-700 mb-1">
                                    Type de consultation
                                </label>
                                <select id="type_consultation" name="type_consultation"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Tous les types</option>
                                    <option value="consultation">Consultation générale</option>
                                    <option value="urgence">Urgence</option>
                                    <option value="suivi">Suivi</option>
                                    <option value="controle">Contrôle</option>
                                    <option value="vaccination">Vaccination</option>
                                </select>
                            </div>

                            <div>
                                <label for="assurance" class="block text-sm font-medium text-gray-700 mb-1">
                                    Assurance
                                </label>
                                <select id="assurance" name="assurance"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Toutes les assurances</option>
                                    <option value="ipm">IPM</option>
                                    <option value="cnss">CNSS</option>
                                    <option value="mutuelle_sante">Mutuelle de Santé</option>
                                    <option value="assurance_privee">Assurance Privée</option>
                                    <option value="aucune">Aucune assurance</option>
                                </select>
                            </div>

                            <div>
                                <label for="date_debut" class="block text-sm font-medium text-gray-700 mb-1">
                                    Date de début
                                </label>
                                <input type="date" id="date_debut" name="date_debut"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            </div>

                            <div>
                                <label for="date_fin" class="block text-sm font-medium text-gray-700 mb-1">
                                    Date de fin
                                </label>
                                <input type="date" id="date_fin" name="date_fin"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            </div>

                            <div>
                                <label for="montant_min" class="block text-sm font-medium text-gray-700 mb-1">
                                    Montant minimum (CFA)
                                </label>
                                <input type="number" id="montant_min" name="montant_min" min="0"
                                       placeholder="0"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                    </div>

                    <!-- Search Actions -->
                    <div class="border-t border-gray-200 pt-6">
                        <div class="flex flex-col sm:flex-row sm:justify-end sm:space-x-3 space-y-3 sm:space-y-0">
                            <button type="button" class="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                Réinitialiser
                            </button>
                            <button type="submit" class="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                                Rechercher
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Search Results -->
        <div class="px-4 sm:px-0">
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Résultats de recherche</h3>
                        <span class="text-sm text-gray-500">24 dossiers trouvés</span>
                    </div>

                    <!-- Results List -->
                    <div class="space-y-4">
                        <!-- Result 1 -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center">
                                            <span class="text-sm font-medium text-white">AS</span>
                                        </div>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Aminata Sow</h4>
                                        <p class="text-sm text-gray-500">+221 77 123 4567</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Consultation
                                    </span>
                                    <span class="text-sm text-gray-500">15 Jan 2024</span>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                                <div>
                                    <span class="text-xs font-medium text-gray-500">Diagnostic:</span>
                                    <p class="text-sm text-gray-900">Hypertension artérielle légère</p>
                                </div>
                                <div>
                                    <span class="text-xs font-medium text-gray-500">Médicaments:</span>
                                    <p class="text-sm text-gray-900">Amlodipine 5mg</p>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex space-x-4">
                                    <span class="text-xs text-gray-500">Frais: <span class="font-medium">15,000 CFA</span></span>
                                    <span class="text-xs text-gray-500">Assurance: <span class="font-medium">IPM</span></span>
                                </div>
                                <div class="flex space-x-2">
                                    <a href="patient-profile.html" class="text-sm text-blue-600 hover:text-blue-900">Voir patient</a>
                                    <a href="edit-record.html" class="text-sm text-green-600 hover:text-green-900">Modifier</a>
                                </div>
                            </div>
                        </div>

                        <!-- Result 2 -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center">
                                            <span class="text-sm font-medium text-white">MD</span>
                                        </div>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Mamadou Diop</h4>
                                        <p class="text-sm text-gray-500">+221 70 987 6543</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Urgence
                                    </span>
                                    <span class="text-sm text-gray-500">12 Jan 2024</span>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                                <div>
                                    <span class="text-xs font-medium text-gray-500">Diagnostic:</span>
                                    <p class="text-sm text-gray-900">Gastro-entérite aiguë</p>
                                </div>
                                <div>
                                    <span class="text-xs font-medium text-gray-500">Médicaments:</span>
                                    <p class="text-sm text-gray-900">Smecta, Spasfon</p>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex space-x-4">
                                    <span class="text-xs text-gray-500">Frais: <span class="font-medium">20,000 CFA</span></span>
                                    <span class="text-xs text-gray-500">Assurance: <span class="font-medium">CNSS</span></span>
                                </div>
                                <div class="flex space-x-2">
                                    <a href="patient-profile.html" class="text-sm text-blue-600 hover:text-blue-900">Voir patient</a>
                                    <a href="edit-record.html" class="text-sm text-green-600 hover:text-green-900">Modifier</a>
                                </div>
                            </div>
                        </div>

                        <!-- Result 3 -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-purple-500 flex items-center justify-center">
                                            <span class="text-sm font-medium text-white">FS</span>
                                        </div>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">Fatou Sall</h4>
                                        <p class="text-sm text-gray-500">+221 76 555 1234</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Suivi
                                    </span>
                                    <span class="text-sm text-gray-500">10 Jan 2024</span>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                                <div>
                                    <span class="text-xs font-medium text-gray-500">Diagnostic:</span>
                                    <p class="text-sm text-gray-900">Suivi grossesse - 2ème trimestre</p>
                                </div>
                                <div>
                                    <span class="text-xs font-medium text-gray-500">Médicaments:</span>
                                    <p class="text-sm text-gray-900">Acide folique, Fer</p>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex space-x-4">
                                    <span class="text-xs text-gray-500">Frais: <span class="font-medium">18,000 CFA</span></span>
                                    <span class="text-xs text-gray-500">Assurance: <span class="font-medium">Mutuelle</span></span>
                                </div>
                                <div class="flex space-x-2">
                                    <a href="patient-profile.html" class="text-sm text-blue-600 hover:text-blue-900">Voir patient</a>
                                    <a href="edit-record.html" class="text-sm text-green-600 hover:text-green-900">Modifier</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Load More -->
                    <div class="mt-6 text-center">
                        <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            Voir plus de résultats
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Export Options -->
        <div class="px-4 sm:px-0 mt-6">
            <div class="bg-white shadow rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-sm font-medium text-gray-900">Exporter les résultats</h3>
                    <div class="flex space-x-2">
                        <button class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-xs font-medium text-gray-700 bg-white hover:bg-gray-50">
                            PDF
                        </button>
                        <button class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-xs font-medium text-gray-700 bg-white hover:bg-gray-50">
                            Excel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
